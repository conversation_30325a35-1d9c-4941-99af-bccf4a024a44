# SMAP - Sistem Manajemen Anti Penyuapan

SMAP adalah aplikasi web internal perusahaan untuk mengelola sistem manajemen anti penyuapan dengan kontrol akses berbasis peran yang ketat.

## 🚀 Fitur Utama

### 👥 Manajemen Pengguna & Role
- **5 Role Pengguna**: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
- **Kontrol Akses Berbasis Role**: Setiap role memiliki akses yang berbeda
- **Autentikasi Aman**: Login dengan email dan password

### 📊 Dashboard
- **Progress Tracking**: Pantau progress pengisian dokumen dengan progress bar
- **Statistik Real-time**: Lihat statistik dokumen yang selesai, dalam progress, dan belum dimulai
- **Role-specific Content**: Konten dashboard disesuaikan dengan role pengguna

### 📄 Manajemen Dokumen
- **4 Dokumen Utama**:
  1. Pedoman Peraturan Perusahaan
  2. Prosedur dan Instruksi Ke<PERSON>ja (buka di tab baru)
  3. Evidence Mapping Klausul (k<PERSON><PERSON>)
  4. Fun<PERSON><PERSON> Anti Penyuapan
- **Dokumen Risk Assessment**: Tersedia untuk semua pengguna
- **Sequential Access**: Dokumen terbuka secara berurutan setelah menyelesaikan dokumen sebelumnya

### 🔔 Sistem Notifikasi
- **Notifikasi Real-time**: Update dokumen, peringatan sistem, dll
- **Badge Counter**: Jumlah notifikasi yang belum dibaca
- **Filter & Kategori**: Filter berdasarkan tipe notifikasi
- **Role-specific Notifications**: Notifikasi disesuaikan dengan role

### 📁 Repository Management
- **File & Folder Management**: Upload, download, dan organize file
- **Permission Control**: Admin Super memiliki akses penuh, Karyawan read-only
- **Breadcrumb Navigation**: Navigasi folder yang mudah
- **File Type Support**: PDF, Word, Excel, PowerPoint, Image

### 📝 Form System
- **Collapsible Sections**: Form terorganisir dalam section yang dapat dilipat
- **Progress Indicator**: Indikator progress pengisian form
- **File Upload**: Upload dokumen pendukung
- **Validation**: Validasi form yang komprehensif
- **Draft & Submit**: Simpan draft atau submit final

### ⚠️ Risk History
- **Risk Tracking**: Pantau riwayat risiko anti penyuapan
- **Status Management**: Open, In Review, Mitigated, Closed
- **Risk Levels**: Low, Medium, High, Critical
- **Filter & Search**: Filter berdasarkan status dan level risiko

## 🎨 Design System

### Brand Guidelines
- **Font**: Gotham & Gotham Rounded (Bold, Medium, Regular)
- **Primary Color**: #EF3F40 (Spirit Telkom)
- **Secondary Colors**: #54565A, #35363A, #000000
- **Neutral**: #FFFFFF
- **Design**: Card-based layout, rounded corners, soft shadows

### Responsive Design
- **Desktop First**: Optimized untuk desktop dan tablet
- **Mobile Friendly**: Responsive design untuk mobile
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)

## 🛠️ Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Icons**: Heroicons
- **Date Handling**: date-fns
- **Form Handling**: React Hook Form
- **Testing**: Jest + Testing Library

## 📦 Installation

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd smap-web-app
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Run Development Server**
   ```bash
   npm run dev
   ```

4. **Open Browser**
   ```
   http://localhost:3000
   ```

## 🔐 Demo Accounts

| Role | Username | Password | Access Level |
|------|----------|----------|--------------|
| Admin Super | 1001 | 1001 | Full Access |
| Admin Biasa | 1002 | 1002 | Limited Admin |
| Reviewer | 1003 | 1003 | Document Review |
| Scopers | 1004 | 1004 | Audit Access |
| Karyawan | 1005 | 1005 | Read Only |

## 🚦 User Flow

### Admin Super & Admin Biasa
1. Login → Dashboard
2. Akses semua dokumen secara berurutan
3. Kelola repository (Admin Super only)
4. Terima semua notifikasi

### Reviewer
1. Login → Dashboard
2. Akses semua dokumen SMAP untuk review
3. Dapat mengakses Evidence Mapping
4. Repository read-only
5. Notifikasi khusus review

### Scopers
1. Login → Dashboard
2. Akses dokumen sesuai role
3. Dapat akses Evidence Mapping
4. Repository read-only

### Karyawan
1. Login → Langsung ke Dokumen SMAP
2. Hanya akses Dokumen Risk Assessment
3. Repository read-only
4. Notifikasi terbatas

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── dashboard/         # Dashboard page
│   ├── dokumen/          # Document management
│   ├── notifikasi/       # Notifications
│   ├── repository/       # File repository
│   ├── form/            # Form system
│   └── login/           # Authentication
├── components/           # Reusable components
│   ├── auth/            # Authentication components
│   ├── dashboard/       # Dashboard components
│   ├── dokumen/         # Document components
│   ├── forms/           # Form components
│   ├── layout/          # Layout components
│   ├── notifications/   # Notification components
│   ├── repository/      # Repository components
│   └── ui/              # UI components
├── hooks/               # Custom hooks
├── lib/                 # Utilities
├── store/               # Zustand stores
└── types/               # TypeScript types
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode

## 🌟 Key Features Implementation

### Role-Based Access Control
```typescript
// Example: Check user permissions
const { user, hasRole, canAccessDocument } = useAuthStore();
const canAccess = canAccessDocument(document.accessibleRoles);
```

### Document Progress Tracking
```typescript
// Example: Update document progress
const { updateProgress, completeDocument } = useDocumentStore();
updateProgress(documentId, userId, 75);
```

### Notification System
```typescript
// Example: Add notification
const { addNotification } = useNotificationStore();
addNotification({
  title: 'Document Updated',
  message: 'New document available',
  type: 'document_update',
  userId: user.id
});
```

## 🔒 Security Features

- **Role-based Access Control**: Strict permission system
- **Route Protection**: Protected routes based on user role
- **Data Validation**: Client and server-side validation
- **Secure Storage**: Encrypted local storage for sensitive data

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px - 1279px
- **Large Desktop**: ≥ 1280px

## 🎯 Performance Optimizations

- **Code Splitting**: Automatic code splitting with Next.js
- **Image Optimization**: Next.js Image component
- **Lazy Loading**: Components loaded on demand
- **Caching**: Efficient state management with Zustand

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

1. **Build Application**
   ```bash
   npm run build
   ```

2. **Start Production Server**
   ```bash
   npm run start
   ```

3. **Deploy to Platform**
   - Vercel (recommended)
   - Netlify
   - AWS
   - Docker

## 📄 License

This project is proprietary software developed for Telkom Indonesia internal use.

## 👥 Support

For support and questions, please contact the development team or create an issue in the repository.

---

**SMAP - Sistem Manajemen Anti Penyuapan**  
*Telkom Indonesia - 2024*
