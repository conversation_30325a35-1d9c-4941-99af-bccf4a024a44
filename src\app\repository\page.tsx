'use client';

import { useState, useEffect } from 'react';
import AppLayout from '@/components/layout/AppLayout';
import { useAuth } from '@/store/auth';
import { useEvents, Event } from '@/store/events';
import EventForm from '@/components/ui/EventForm';
import EventsList from '@/components/ui/EventsList';
import { PlusIcon, XMarkIcon, FolderIcon } from '@heroicons/react/24/outline';

export default function RepositoryPage() {
  const { user } = useAuth();
  const { initializeEvents } = useEvents();
  const [showForm, setShowForm] = useState(false);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);

  // Initialize events data
  useEffect(() => {
    initializeEvents();
  }, [initializeEvents]);

  // Redirect if not super admin
  if (!user || user.role !== 'admin_super') {
    if (typeof window !== 'undefined') {
      window.location.href = '/dashboard';
    }
    return null;
  }

  const handleCreateEvent = () => {
    setEditingEvent(null);
    setShowForm(true);
  };

  const handleEditEvent = (event: Event) => {
    setEditingEvent(event);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingEvent(null);
  };

  const handleSaveEvent = () => {
    // Form will handle the save, we just need to refresh
    setShowForm(false);
    setEditingEvent(null);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header Container */}
        <div className="gradient-primary rounded-2xl p-8 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div>
                <h1 className="font-gotham-rounded text-3xl font-bold text-white mb-2">
                  Repository
                </h1>
                <p className="font-gotham text-white/90">
                  Kelola event dan program SMAP
                </p>
              </div>
            </div>
            <div className="hidden md:block">
              <FolderIcon className="h-12 w-12 text-white/80" />
            </div>
          </div>
        </div>

        {/* Create Event Button */}
        <div className="flex justify-end">
          <button
            onClick={handleCreateEvent}
            className="inline-flex items-center px-4 py-2 bg-primary text-white font-gotham-rounded font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Create Event
          </button>
        </div>

        {/* Create/Edit Form Modal */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="font-gotham-rounded text-2xl font-bold text-gray-900">
                    {editingEvent ? 'Edit Event' : 'Create New Event'}
                  </h2>
                  <button
                    onClick={handleCloseForm}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                  >
                    <XMarkIcon className="h-6 w-6 text-gray-500" />
                  </button>
                </div>

                <EventForm
                  event={editingEvent}
                  onClose={handleCloseForm}
                  onSave={handleSaveEvent}
                />
              </div>
            </div>
          </div>
        )}

        {/* Events List */}
        <div className="card">
          <div className="pb-6 border-b border-gray-100">
            <h2 className="font-gotham-rounded text-2xl font-bold text-gray-900">
              Event List
            </h2>
            <p className="font-gotham text-secondary mt-1">
              Daftar semua event yang telah dibuat
            </p>
          </div>
          
          <div className="p-6">
            <EventsList onEditEvent={handleEditEvent} />
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
