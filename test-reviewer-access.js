// Test script to verify reviewer role access to SMAP documents
// This script simulates the reviewer role and checks document access

const { useDocuments } = require('./src/store/documents.ts');

// Simulate reviewer role
const reviewerRole = 'reviewer';

// Test function
function testReviewerAccess() {
  console.log('🧪 Testing Reviewer Role Access to SMAP Documents...\n');
  
  // Get documents store (this would normally be done through React hooks)
  const documentsStore = useDocuments.getState();
  
  // Get accessible documents for reviewer role
  const accessibleDocuments = documentsStore.getAccessibleDocuments(reviewerRole);
  
  console.log(`📊 Total documents accessible to reviewer: ${accessibleDocuments.length}`);
  console.log('\n📋 Accessible Documents:');
  
  accessibleDocuments.forEach((doc, index) => {
    console.log(`${index + 1}. ${doc.title}`);
    console.log(`   Description: ${doc.description}`);
    console.log(`   ID: ${doc.id}`);
    console.log(`   Locked: ${doc.isLocked ? 'Yes' : 'No'}`);
    console.log('');
  });
  
  // Check if SMAP document (doc-1) is accessible
  const smapDocument = accessibleDocuments.find(doc => doc.id === 'doc-1');
  
  if (smapDocument) {
    console.log('✅ SUCCESS: Reviewer can access SMAP document (Pedoman Peraturan Perusahaan)');
    console.log(`   Document Title: ${smapDocument.title}`);
    console.log(`   Document is ${smapDocument.isLocked ? 'locked' : 'unlocked'}`);
  } else {
    console.log('❌ FAILED: Reviewer cannot access SMAP document');
  }
  
  // Check Evidence Mapping access
  const evidenceMapping = accessibleDocuments.find(doc => doc.id === 'doc-3');
  
  if (evidenceMapping) {
    console.log('✅ SUCCESS: Reviewer can access Evidence Mapping document');
  } else {
    console.log('❌ FAILED: Reviewer cannot access Evidence Mapping document');
  }
  
  console.log('\n🎯 Test Summary:');
  console.log(`- Total accessible documents: ${accessibleDocuments.length}`);
  console.log(`- SMAP access: ${smapDocument ? 'YES' : 'NO'}`);
  console.log(`- Evidence Mapping access: ${evidenceMapping ? 'YES' : 'NO'}`);
  
  return accessibleDocuments.length > 0;
}

// Run the test
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testReviewerAccess };
} else {
  testReviewerAccess();
}
