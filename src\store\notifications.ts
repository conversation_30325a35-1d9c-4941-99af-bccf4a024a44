import { create } from 'zustand';
import { Notification, NotificationType, UserRole, DocumentType, NotificationFilter } from '@/types';

// Mock notifications generator with document history
const generateNotifications = (userId: string, userRole: UserRole): Notification[] => {
  const documentHistoryNotifications = [
    {
      title: 'Dokumen Diperbarui',
      message: '<PERSON>kumen "Pedoman Peraturan Perusaha<PERSON>" telah diperbarui.',
      type: 'document_update' as NotificationType,
      isRead: false,
      actionUrl: '/dokumen',
      documentName: 'Pedoman Peraturan Perusahaan',
      documentType: 'Manual' as DocumentType,
      updatedBy: '<PERSON>ki',
      updateAction: 'updated' as const,
    },
    {
      title: 'Dokumen Baru Dibuat',
      message: 'Dokumen "Prosedur Anti Korupsi" telah dibuat.',
      type: 'document_update' as NotificationType,
      isRead: false,
      actionUrl: '/dokumen',
      documentName: 'Prosedur Anti Korupsi',
      documentType: 'Prosedur' as DocumentType,
      updatedBy: 'Siti N<PERSON>',
      updateAction: 'created' as const,
    },
    {
      title: 'Evidence Diupload',
      message: 'Evidence "Laporan Audit Internal" telah diupload.',
      type: 'document_update' as NotificationType,
      isRead: true,
      actionUrl: '/dokumen',
      documentName: 'Laporan Audit Internal',
      documentType: 'Evidence' as DocumentType,
      updatedBy: 'Budi Santoso',
      updateAction: 'created' as const,
    },
    {
      title: 'Dokumen Direview',
      message: 'Dokumen "Policy Anti Penyuapan" telah direview.',
      type: 'document_update' as NotificationType,
      isRead: true,
      actionUrl: '/dokumen',
      documentName: 'Policy Anti Penyuapan',
      documentType: 'Policy' as DocumentType,
      updatedBy: 'Dr. Indira Sari',
      updateAction: 'reviewed' as const,
    },
    {
      title: 'Form Template Diperbarui',
      message: 'Template "Form Pelaporan Gratifikasi" telah diperbarui.',
      type: 'document_update' as NotificationType,
      isRead: false,
      actionUrl: '/dokumen',
      documentName: 'Form Pelaporan Gratifikasi',
      documentType: 'Form' as DocumentType,
      updatedBy: 'Eko Prasetyo',
      updateAction: 'updated' as const,
    },
  ];

  const systemNotifications = [
    {
      title: 'Sistem Maintenance',
      message: 'Sistem akan menjalani maintenance pada tanggal 30 Januari 2024 pukul 02:00 WIB.',
      type: 'warning' as NotificationType,
      isRead: true,
      actionUrl: null,
    },
  ];

  const roleSpecificNotifications: Record<UserRole, any[]> = {
    admin_super: [
      {
        title: 'Laporan Bulanan Siap',
        message: 'Laporan bulanan SMAP untuk bulan Januari 2024 telah siap untuk direview.',
        type: 'success',
        isRead: false,
        actionUrl: '/reports',
      },
    ],
    admin_biasa: [
      {
        title: 'Dokumen Perlu Review',
        message: 'Terdapat 2 dokumen yang memerlukan review dari administrator.',
        type: 'warning',
        isRead: false,
        actionUrl: '/dokumen',
      },
    ],

    scopers: [
      {
        title: 'Evidence Mapping Tersedia',
        message: 'Dokumen Evidence Mapping Klausul telah tersedia untuk unit audit Anda.',
        type: 'document_update',
        isRead: false,
        actionUrl: '/dokumen',
      },
    ],
    reviewer: [
      {
        title: 'Dokumen SMAP Siap Review',
        message: 'Dokumen Pedoman Sistem Manajemen Anti Penyuapan telah siap untuk direview.',
        type: 'document_update',
        isRead: false,
        actionUrl: '/dokumen',
      },
      {
        title: 'Review Deadline Reminder',
        message: 'Reminder: Review dokumen SMAP harus diselesaikan dalam 3 hari.',
        type: 'warning',
        isRead: false,
        actionUrl: '/dokumen',
      },
    ],
    karyawan: [
      {
        title: 'Training Anti Korupsi',
        message: 'Training wajib anti korupsi akan diadakan pada tanggal 15 Februari 2024.',
        type: 'info',
        isRead: false,
        actionUrl: '/training',
      },
    ],
  };

  const allNotifications = [
    ...documentHistoryNotifications,
    ...systemNotifications,
    ...(roleSpecificNotifications[userRole] || []),
  ];

  return allNotifications.map((notification, index) => ({
    ...notification,
    id: `notif-${userId}-${index + 1}`,
    userId,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
  })).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()); // Sort by newest first
};

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  initializeNotifications: (userId: string, userRole: UserRole) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: (userId: string) => void;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  getUserNotifications: (userId: string) => Notification[];
  getFilteredNotifications: (userId: string, filter?: NotificationFilter) => Notification[];
  getRecentNotifications: (userId: string, limit?: number) => Notification[];
}

export const useNotifications = create<NotificationState>((set, get) => ({
  notifications: [],
  unreadCount: 0,

  initializeNotifications: (userId: string, userRole: UserRole) => {
    const notifications = generateNotifications(userId, userRole);
    const unreadCount = notifications.filter(n => !n.isRead).length;
    
    set({ notifications, unreadCount });
  },

  markAsRead: (notificationId: string) => {
    set((state) => {
      const updatedNotifications = state.notifications.map(notification =>
        notification.id === notificationId
          ? { ...notification, isRead: true }
          : notification
      );
      
      const unreadCount = updatedNotifications.filter(n => !n.isRead).length;
      
      return { notifications: updatedNotifications, unreadCount };
    });
  },

  markAllAsRead: (userId: string) => {
    set((state) => {
      const updatedNotifications = state.notifications.map(notification =>
        notification.userId === userId
          ? { ...notification, isRead: true }
          : notification
      );
      
      return { notifications: updatedNotifications, unreadCount: 0 };
    });
  },

  addNotification: (notification) => {
    const newNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}`,
      createdAt: new Date(),
    };
    
    set((state) => ({
      notifications: [newNotification, ...state.notifications],
      unreadCount: state.unreadCount + 1,
    }));
  },

  getUserNotifications: (userId: string) => {
    const { notifications } = get();
    return notifications.filter(n => n.userId === userId);
  },

  getFilteredNotifications: (userId: string, filter?: NotificationFilter) => {
    const { notifications } = get();
    let filtered = notifications.filter(n => n.userId === userId);

    if (filter) {
      if (filter.documentType) {
        filtered = filtered.filter(n => n.documentType === filter.documentType);
      }
      if (filter.dateFrom) {
        filtered = filtered.filter(n => n.createdAt >= filter.dateFrom!);
      }
      if (filter.dateTo) {
        filtered = filtered.filter(n => n.createdAt <= filter.dateTo!);
      }
      if (filter.updatedBy) {
        filtered = filtered.filter(n =>
          n.updatedBy?.toLowerCase().includes(filter.updatedBy!.toLowerCase())
        );
      }
      if (filter.isRead !== undefined) {
        filtered = filtered.filter(n => n.isRead === filter.isRead);
      }
    }

    return filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  },

  getRecentNotifications: (userId: string, limit: number = 5) => {
    const { notifications } = get();
    return notifications
      .filter(n => n.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  },
}));

// Notification type configurations
export const notificationConfig = {
  info: { icon: '💡', color: 'bg-blue-50 border-blue-200 text-blue-900' },
  warning: { icon: '⚠️', color: 'bg-yellow-50 border-yellow-200 text-yellow-900' },
  success: { icon: '✅', color: 'bg-green-50 border-green-200 text-green-900' },
  error: { icon: '❌', color: 'bg-red-50 border-red-200 text-red-900' },
  document_update: { icon: '📄', color: 'bg-red-50 border-red-200 text-red-900' },
};
