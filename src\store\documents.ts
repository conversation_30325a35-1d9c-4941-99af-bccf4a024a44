import { create } from 'zustand';
import { Document, UserRole } from '@/types';

// Mock documents data
const DOCUMENTS: Document[] = [
  {
    id: 'doc-1',
    title: 'Pedoman Peraturan Peru<PERSON>haan',
    description: 'Dokumen pedoman dan peraturan perusahaan terkait anti penyuapan',
    progress: 0,
    isCompleted: false,
    isLocked: false,
    accessibleRoles: ['admin_super', 'admin_biasa', 'scopers', 'reviewer'],
    order: 1,
  },
  {
    id: 'doc-2',
    title: 'Prosedur dan Instruksi Kerja',
    description: 'Prosedur operasional standar dan instruksi kerja',
    progress: 0,
    isCompleted: false,
    isLocked: true,
    accessibleRoles: ['admin_super', 'admin_biasa', 'scopers', 'reviewer'],
    order: 2,
    openInNewTab: true,
  },
  {
    id: 'doc-3',
    title: 'Evidence Mapping Klausul',
    description: 'Pemetaan evidence untuk setiap klausul (khus<PERSON>)',
    progress: 0,
    isCompleted: false,
    isLocked: true,
    accessibleRoles: ['admin_super', 'scopers', 'reviewer'],
    order: 3,
  },
  {
    id: 'doc-4',
    title: 'Fungsi Kepatuhan Anti Penyuapan',
    description: 'Dokumen fungsi kepatuhan dan monitoring',
    progress: 0,
    isCompleted: false,
    isLocked: true,
    accessibleRoles: ['admin_super', 'admin_biasa', 'scopers', 'reviewer'],
    order: 4,
  },
  {
    id: 'doc-risk',
    title: 'Dokumen Risk Assessment',
    description: 'Penilaian risiko anti penyuapan (tersedia untuk semua)',
    progress: 0,
    isCompleted: false,
    isLocked: false,
    accessibleRoles: ['admin_super', 'admin_biasa', 'scopers', 'karyawan', 'reviewer'],
    order: 0,
  },
];

interface DocumentState {
  documents: Document[];
  userProgress: Record<string, Record<string, number>>;
  getAccessibleDocuments: (userRole: UserRole) => Document[];
  updateProgress: (documentId: string, userId: string, progress: number) => void;
  completeDocument: (documentId: string, userId: string) => void;
  getDocumentProgress: (documentId: string, userId: string) => number;
  isDocumentUnlocked: (documentId: string, userId: string, userRole: UserRole) => boolean;
}

export const useDocuments = create<DocumentState>((set, get) => ({
  documents: DOCUMENTS,
  userProgress: {},

  getAccessibleDocuments: (userRole: UserRole) => {
    const { documents } = get();
    return documents
      .filter(doc => doc.accessibleRoles.includes(userRole))
      .sort((a, b) => a.order - b.order);
  },

  updateProgress: (documentId: string, userId: string, progress: number) => {
    set((state) => ({
      userProgress: {
        ...state.userProgress,
        [userId]: {
          ...state.userProgress[userId],
          [documentId]: Math.min(100, Math.max(0, progress)),
        },
      },
    }));
  },

  completeDocument: (documentId: string, userId: string) => {
    const { updateProgress } = get();
    updateProgress(documentId, userId, 100);
    
    // Unlock next document
    set((state) => {
      const documents = [...state.documents];
      const currentDoc = documents.find(d => d.id === documentId);
      if (currentDoc) {
        const nextDoc = documents.find(d => d.order === currentDoc.order + 1);
        if (nextDoc) {
          nextDoc.isLocked = false;
        }
      }
      return { documents };
    });
  },

  getDocumentProgress: (documentId: string, userId: string) => {
    const { userProgress } = get();
    return userProgress[userId]?.[documentId] || 0;
  },

  isDocumentUnlocked: (documentId: string, userId: string, userRole: UserRole) => {
    const { documents, getDocumentProgress } = get();
    const doc = documents.find(d => d.id === documentId);
    
    if (!doc) return false;
    if (!doc.accessibleRoles.includes(userRole)) return false;
    if (!doc.isLocked) return true;
    
    // Check if previous document is completed
    const prevDoc = documents.find(d => d.order === doc.order - 1);
    if (!prevDoc) return true;
    
    return getDocumentProgress(prevDoc.id, userId) === 100;
  },
}));
